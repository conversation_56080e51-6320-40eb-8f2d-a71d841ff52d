"use client";

import React, { useState, useEffect, memo } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/shared/components/ui/card";
import { getReviewsStats } from '../actions/reviews.action';

interface Stats {
  total: number;
  published: number;
  pending: number;
  flagged: number;
  hidden: number;
  averageRating: number;
  replied: number;
}

const renderStars = (rating: number) => {
  const stars = [];
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 >= 0.5;
  
  for (let i = 0; i < 5; i++) {
    if (i < fullStars) {
      stars.push(
        <span key={i} className="text-yellow-400 text-xl">★</span>
      );
    } else if (i === fullStars && hasHalfStar) {
      stars.push(
        <span key={i} className="text-yellow-400 text-xl">☆</span>
      );
    } else {
      stars.push(
        <span key={i} className="text-gray-300 text-xl">☆</span>
      );
    }
  }
  
  return stars;
};

interface ReviewsStatsGridProps {
  refreshTrigger?: number; // Optional prop to trigger refresh when stats should update
}

const ReviewsStatsGrid: React.FC<ReviewsStatsGridProps> = ({ refreshTrigger }) => {
  const [stats, setStats] = useState<Stats>({
    total: 0,
    published: 0,
    pending: 0,
    flagged: 0,
    hidden: 0,
    averageRating: 0,
    replied: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await getReviewsStats();

        if (response.success && response.data) {
          setStats(response.data);
        } else {
          setError(response.message || 'Failed to fetch review statistics');
        }
      } catch (error) {
        console.error('Failed to fetch review stats:', error);
        setError('An unexpected error occurred while fetching review statistics');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [refreshTrigger]); // Now depends on refreshTrigger

  const retryFetchStats = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await getReviewsStats();
      
      if (response.success && response.data) {
        setStats(response.data);
      } else {
        setError(response.message || 'Failed to fetch review statistics');
      }
    } catch (error) {
      console.error('Failed to fetch review stats:', error);
      setError('An unexpected error occurred while fetching review statistics');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        {[...Array(4)].map((_, index) => (
          <Card key={index} className="w-[373px] h-[108px] flex flex-col justify-center">
            <CardHeader className="pb-1">
              <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card className="w-[373px] h-[108px] flex flex-col justify-center col-span-full">
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-red-600 text-sm">{error}</p>
              <button 
                onClick={retryFetchStats} 
                className="mt-2 text-blue-600 text-sm hover:underline"
              >
                Try again
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <Card className="w-[373px] h-[108px] flex flex-col justify-center">
        <CardHeader className="pb-1">
          <CardTitle className="text-sm font-medium text-gray-600">Total Reviews</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="text-2xl font-bold">{stats.total}</div>
        </CardContent>
      </Card>
      <Card className="w-[373px] h-[108px] flex flex-col justify-center">
        <CardHeader className="pb-1">
          <CardTitle className="text-sm font-medium text-gray-600">Average Rating</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex items-center gap-2">
            <div className="text-2xl font-bold">{stats.averageRating.toFixed(1)}</div>
            <div className="flex">{renderStars(Math.round(stats.averageRating))}</div>
          </div>
        </CardContent>
      </Card>
      <Card className="w-[373px] h-[108px] flex flex-col justify-center">
        <CardHeader className="pb-1">
          <CardTitle className="text-sm font-medium text-gray-600">Replied</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="text-2xl font-bold text-green-600">{stats.replied}</div>
        </CardContent>
      </Card>
      <Card className="w-[373px] h-[108px] flex flex-col justify-center">
        <CardHeader className="pb-1">
          <CardTitle className="text-sm font-medium text-gray-600">Flagged</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="text-2xl font-bold text-orange-600">{stats.flagged}</div>
        </CardContent>
      </Card>
    </div>
  );
};

// Memoize the component to prevent unnecessary re-renders
// Only re-render when refreshTrigger prop changes
export default memo(ReviewsStatsGrid);