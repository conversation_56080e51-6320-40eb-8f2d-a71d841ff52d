import { Reply, Flag, MessageSquare, StickyNote } from 'lucide-react';
import { But<PERSON> } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { Textarea } from '@/shared/components/ui/textarea';
import { TReview } from '../types/review.type';
import ReviewStars from './review-stars';
import QuickReplyOptions from './quick-reply-options';
import ReportReviewModal from './report-review-modal';
import { useState } from 'react';
import { replyToReview } from '../actions/reviews.action';
import { successToast, errorToast } from '@/shared/lib/toast';

interface ReviewCardProps {
  review: TReview;
  onStatusChange: (id: string, status: TReview['status']) => void;
  onFlag: (id: string, reason: string) => void;
  onRestore: (id: string) => void;
  onHide: (id: string, reason: string) => void;
  onPublish: (id: string) => void;
  onViewDetails: (id: string) => void;
  onAddReview: () => void;
  onAddNote?: (id: string, note: string) => void;
  onReply?: (id: string) => void;
  onReplyUpdate?: (id: string, replyText: string) => void;
}

const ReviewCard = ({
  review,
  onStatusChange,
  onFlag,
  onRestore,
  onHide,
  onPublish,
  onViewDetails,
  onAddReview,
  onAddNote,
  onReply,
  onReplyUpdate,
}: ReviewCardProps) => {
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyText, setReplyText] = useState('');
  const [isSubmittingReply, setIsSubmittingReply] = useState(false);
  const [isReportModalOpen, setIsReportModalOpen] = useState(false);
  const [selectedReviewForReport, setSelectedReviewForReport] = useState<TReview | null>(null);

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'published':
        return 'default' as const; // Green badge for published status
      case 'pending':
        return 'secondary';
      case 'flagged':
        return 'destructive';
      case 'hidden':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  const handleQuickReply = (message: string) => {
    setReplyText(message);
  };

  const handleReply = async (id: string) => {
    if (!replyText.trim()) {
      errorToast('Please enter a reply message');
      return;
    }

    setIsSubmittingReply(true);
    
    try {
      const response = await replyToReview(id, replyText.trim());
      
      if (response.success) {
        successToast('Reply sent successfully');
        
        // Call the parent component to update the review data
        if (onReplyUpdate) {
          onReplyUpdate(id, replyText.trim());
        }
        
        // Reset reply state
        setReplyingTo(null);
        setReplyText('');
      } else {
        errorToast(response.message || 'Failed to send reply');
      }
    } catch (error) {
      console.error('Error sending reply:', error);
      errorToast('An unexpected error occurred while sending reply');
    } finally {
      setIsSubmittingReply(false);
    }
  };

  const handleFlag = (reviewToFlag: TReview) => {
    setSelectedReviewForReport(reviewToFlag);
    setIsReportModalOpen(true);
  };

  const handleReportSubmit = async (reviewId: string, reason: string) => {
    await onFlag(reviewId, reason);
    setIsReportModalOpen(false);
    setSelectedReviewForReport(null);
  };

  // Check if review has existing replies
  const hasReplies = review.replyText && review.replyText.trim().length > 0;
  const replyDate = review.replyDate ? formatDate(review.replyDate) : null;
  const hasNotes = review.comments && review.comments.length > 0;

  return (
    
    <div className={`border-l-4 rounded-md bg-white shadow-sm ${review.flagged ? 'border-l-red-500' : 'border-l-blue-500'} mb-4`}>
      <div className="p-6">
        <div className="flex justify-between items-start mb-4">
          <div>
            <div className="flex items-center gap-2 mb-2">
              <h3 className="font-semibold flex items-center gap-2">
                {review.flagged && (
                  <Flag className="h-4 w-4 text-red-500" />
                )}
                {review.reviewerName}
              </h3>
              <div className="flex">
                <ReviewStars rating={review.rating} showNumber={false} />
              </div>
              <Badge 
                variant={getStatusBadgeVariant(review.status)}
                className={review.status === 'published' ? 'bg-green-100 text-green-800 border-green-200' : ''}
              >
                {review.status}
              </Badge>
              {review.flagged && (
                <Badge variant="destructive" className="ml-2 bg-red-100 text-red-800 border-red-300 animate-pulse">
                  <Flag className="h-4 w-4 mr-1 text-red-600" />
                  Flagged
                </Badge>
              )}
            </div>
            {/* <p className="text-sm text-gray-600 mb-1">{review.targetName}</p> */}
            <p className="text-sm text-gray-500">{formatDate(review.createdAt)}</p>
          </div>
          <div className="flex gap-2">
            {!hasReplies && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setReplyingTo(review.id)}
              >
                <Reply className="h-4 w-4 mr-1" />
                Reply
              </Button>
            )}
            {!review.flagged && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleFlag(review)}
              >
                <Flag className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>

        <p className="text-gray-700 mb-4">{review.comment}</p>

        {review.status === 'hidden' && review.hiddenReason && (
          <div className="bg-yellow-50 p-3 rounded-lg mb-4 border-l-4 border-yellow-400">
            <div className="flex items-center gap-2 mb-1">
              <Flag className="h-4 w-4 text-yellow-600" />
              <span className="text-sm font-semibold text-yellow-800">Hidden Reason:</span>
            </div>
            <p className="text-sm text-yellow-700">{review.hiddenReason}</p>
          </div>
        )}

        {hasReplies && (
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <MessageSquare className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-semibold text-blue-600">Your Reply:</span>
              {replyDate && (
                <span className="text-xs text-gray-500 ml-auto">{replyDate}</span>
              )}
            </div>
            <p className="text-sm text-gray-700">{review.replyText}</p>
          </div>
        )}

        {/* {hasNotes && (
          <div className="bg-gray-50 p-4 rounded-lg mt-4">
            <div className="flex items-center gap-2 mb-3">
              <StickyNote className="h-4 w-4 text-gray-600" />
              <span className="text-sm font-semibold text-gray-600">Admin Notes:</span>
            </div>
            <div className="space-y-2">
              {review.comments?.map((comment, index) => (
                <div key={comment.id} className="border-l-2 border-gray-300 pl-3 py-1">
                  <p className="text-sm text-gray-700">{comment.comment}</p>
                  <div className="flex justify-between items-center mt-1">
                    <span className="text-xs text-gray-500">by {comment.createdBy}</span>
                    <span className="text-xs text-gray-500">{formatDate(comment.createdAt)}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )} */}

        {replyingTo === review.id && (
          <div className="mt-4 space-y-3">
            <QuickReplyOptions
              onSelectReply={handleQuickReply}
              reviewRating={review.rating}
            />
            <Textarea
              placeholder="Write your reply or select from quick options above..."
              value={replyText}
              onChange={(e) => setReplyText(e.target.value)}
              rows={4}
            />
            <div className="flex gap-2">
              <Button
                size="sm"
                onClick={() => handleReply(review.id)}
                disabled={!replyText.trim() || isSubmittingReply}
              >
                {isSubmittingReply ? 'Sending...' : 'Send Reply'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setReplyingTo(null);
                  setReplyText("");
                }}
                disabled={isSubmittingReply}
              >
                Cancel
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Report Review Modal */}
      <ReportReviewModal
        isOpen={isReportModalOpen}
        onClose={() => {
          setIsReportModalOpen(false);
          setSelectedReviewForReport(null);
        }}
        review={selectedReviewForReport}
        onSubmit={handleReportSubmit}
      />
    </div>
    
  );
};

export default ReviewCard;
