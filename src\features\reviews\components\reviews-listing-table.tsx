import { getReviews } from "@/features/reviews/actions/reviews.action";
import { TReviewLegacy } from "@/features/reviews/types/review.type";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/shared/components/ui/table";
import Image from "next/image";

const ReviewsListingTable = async () => {
  const { data: reviews } = await getReviews();

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>User</TableHead>
          <TableHead>Property</TableHead>
          <TableHead>Rating</TableHead>
          <TableHead>Review</TableHead>
          <TableHead>Date</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {reviews.map((review: TReviewLegacy) => (
          <TableRow key={review.id}>
            <TableCell>
              <div className="flex items-center gap-2">
                <Image
                  src={review.user.image}
                  alt={review.user.name}
                  width={32}
                  height={32}
                  className="rounded-full"
                />
                {review.user.name}
              </div>
            </TableCell>
            <TableCell>{review.property.name}</TableCell>
            <TableCell>{review.rating}</TableCell>
            <TableCell>{review.review}</TableCell>
            <TableCell>{new Date(review.createdAt).toLocaleDateString()}</TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

export default ReviewsListingTable;
