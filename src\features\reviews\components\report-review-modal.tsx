'use client';

import { useState } from 'react';
import { Button } from '@/shared/components/ui/button';
import { Textarea } from '@/shared/components/ui/textarea';
import { Label } from '@/shared/components/ui/label';
import { X, Flag, AlertTriangle } from 'lucide-react';
import Modal from '@/shared/components/modals/modal';
import { TReview } from '../types/review.type';

interface ReportReviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  review: TReview | null;
  onSubmit: (reviewId: string, reason: string) => Promise<void>;
}

const ReportReviewModal = ({ isOpen, onClose, review, onSubmit }: ReportReviewModalProps) => {
  const [reason, setReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!review || !reason.trim()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit(review.id, reason.trim());
      setReason('');
      onClose();
    } catch (error) {
      console.error('Error submitting report:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setReason('');
      onClose();
    }
  };

  if (!review) return null;

  return (
    <Modal isOpen={isOpen} onClose={handleClose} classes="max-w-md">
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Flag className="h-5 w-5 text-red-600" />
            <h2 className="text-lg font-semibold text-gray-900">Report Review</h2>
          </div>
          <button
            onClick={handleClose}
            disabled={isSubmitting}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            aria-label="Close modal"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Warning */}
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
          <div className="flex items-start space-x-2">
            <AlertTriangle className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-amber-800">
              <p className="font-medium">Please report responsibly</p>
              <p className="mt-1">Only flag reviews that violate our community guidelines or contain inappropriate content.</p>
            </div>
          </div>
        </div>

        {/* Review Details */}
        <div className="bg-gray-50 rounded-lg p-4 space-y-3">
          <div>
            <Label className="text-sm font-medium text-gray-700">Customer Name</Label>
            <p className="text-sm text-gray-900 mt-1">{review.reviewerName}</p>
          </div>
          <div>
            <Label className="text-sm font-medium text-gray-700">Review Comment</Label>
            <p className="text-sm text-gray-900 mt-1 bg-white p-3 rounded border">
              {review.comment}
            </p>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="reason" className="text-sm font-medium text-gray-700">
              Reason for reporting *
            </Label>
            <Textarea
              id="reason"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder="Please provide a detailed reason for flagging this review..."
              rows={4}
              className="mt-1"
              disabled={isSubmitting}
              required
            />
            <p className="text-xs text-gray-500 mt-1">
              Provide specific details about why this review should be flagged.
            </p>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
              className="px-4 py-2"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!reason.trim() || isSubmitting}
              className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white"
            >
              {isSubmitting ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>Reporting...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <Flag className="h-4 w-4" />
                  <span>Report Review</span>
                </div>
              )}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
};

export default ReportReviewModal;
