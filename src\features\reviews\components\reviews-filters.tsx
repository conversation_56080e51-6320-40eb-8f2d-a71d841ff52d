import { Search, X } from 'lucide-react';

interface ReviewsFiltersProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  selectedFilter: string;
  onFilterChange: (value: string) => void;
}

const ReviewsFilters = ({
  searchTerm,
  onSearch<PERSON>hange,
  selectedFilter,
  onFilterChange,
}: ReviewsFiltersProps) => {
  const clearSearch = () => {
    onSearchChange('');
  };

  return (
    // <div className="bg-white rounded-lg shadow">
      <div className="flex gap-4 p-6 ">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
          <input
            placeholder="Search reviews..."
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10 pr-10 py-2 border border-gray-300 rounded-md w-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          {searchTerm && (
            <button
              onClick={clearSearch}
              className="absolute right-3 top-3 h-4 w-4 text-gray-400 hover:text-gray-600"
              title="Clear search"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
        <div className="relative">
          <select
            value={selectedFilter}
            onChange={(e) => onFilterChange(e.target.value)}
            className={`px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[180px] ${
              selectedFilter !== 'all' ? 'bg-blue-50 border-blue-300' : ''
            }`}
            aria-label="Filter reviews"
          >
            <option value="all">All Reviews</option>
            <option value="replied">Replied</option>
            <option value="unreplied">Unreplied</option>
            <option value="flagged">Flagged</option>
            <option value="published">Published</option>
            <option value="high_rating">High Rating (4-5★)</option>
            <option value="low_rating">Low Rating (1-2★)</option>
          </select>
        </div>
      </div>
    // </div>
  );
};

export default ReviewsFilters;
