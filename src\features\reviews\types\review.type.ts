export interface ModerationRequest {
  reason: string;
  requestedBy: string;
  requestDate: string;
  notes?: string;
}

export interface ReviewComment {
  id: string;
  comment: string;
  createdAt: string;
  createdBy: string;
  type: 'note' | 'status_change' | 'moderation';
}

// API Response types (matching the actual API structure)
export interface ApiReviewNote {
  id: number;
  note: string;
  createdAt: string;
  adminName: string;
}

export interface ApiReview {
  id: number;
  reviewerId: number;
  revieweeId: number;
  reviewText: string;
  rating: number;
  statusId: number;
  hideReason: string | null;
  flagged: boolean;
  created_at: string;
  updated_at: string;
  replyText: string | null;
  replyDate: string | null;
  repliedBy: number | null;
  reviewerFirstName: string | null;
  reviewerLastName: string | null;
  reviewerEmail: string;
  reviewerType: string | null;
  revieweeFirstName: string | null;
  revieweeLastName: string | null;
  revieweeType: string;
  agencyName: string | null;
  statusName: string;
  notes: ApiReviewNote[];
}

export interface ApiReviewsResponse {
  reviews: ApiReview[];
  pagination: {
    total: number;
    totalPages: number;
    currentPage: number;
    perPage: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Frontend types (for UI components)
export interface TReview {
  id: string;
  reviewerName: string;
  reviewerEmail: string;
  targetType: 'agent' | 'agency';
  targetName: string;
  targetId: string;
  rating: number;
  comment: string;
  status: 'published' | 'pending' | 'flagged' | 'hidden';
  moderationRequest?: ModerationRequest;
  createdAt: string;
  updatedAt: string;
  hiddenReason?: string;
  comments?: ReviewComment[];
  replyText?: string;
  replyDate?: string;
  repliedBy?: string;
  flagged?: boolean;
}

export interface ReviewStats {
  total: number;
  published: number;
  pending: number;
  flagged: number;
  hidden: number;
  averageRating: number;
  replied: number;
}

// API Stats Response type (matching the API response structure)
export interface ApiReviewStats {
  totalReviews: number;
  averageRating: number;
  repliedCount: number;
  flaggedCount: number;
}

// Legacy type for backward compatibility
export type TReviewLegacy = {
  id: string;
  rating: number;
  review: string;
  user: {
    id: string;
    name: string;
    image: string;
  };
  property: {
    id: string;
    name: string;
  };
  createdAt: string;
};
