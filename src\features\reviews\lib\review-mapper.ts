import { ApiReview, TReview, ReviewComment } from '../types/review.type';

/**
 * Maps API review response to frontend review type
 */
export function mapApiReviewToFrontend(apiReview: ApiReview): TReview {
    // Map status name to frontend status
    const statusMap: Record<string, TReview['status']> = {
        'Pending': 'pending',
        'Publish': 'published',
        'Published': 'published',
        'Flagged': 'flagged',
        'Hidden': 'hidden',
    };

    // Map notes to comments
    const comments: ReviewComment[] = apiReview.notes.map(note => ({
        id: note.id.toString(),
        comment: note.note,
        createdAt: note.createdAt,
        createdBy: note.adminName,
        type: 'note' as const,
    }));

    // Determine target type and name
    const targetType: 'agent' | 'agency' = apiReview.agencyName ? 'agency' : 'agent';
    const targetName = apiReview.agencyName ||
        `${apiReview.revieweeFirstName || ''} ${apiReview.revieweeLastName || ''}`.trim() ||
        'Unknown';

    // Build reviewer name
    const reviewerName = `${apiReview.reviewerFirstName || ''} ${apiReview.reviewerLastName || ''}`.trim() ||
        "N/A";


    return {
        id: apiReview.id.toString(),
        reviewerName,
        reviewerEmail: apiReview.reviewerEmail,
        targetType,
        targetName,
        targetId: apiReview.revieweeId.toString(),
        rating: apiReview.rating,
        comment: apiReview.reviewText,
        status: statusMap[apiReview.statusName] || 'pending',
        createdAt: apiReview.created_at,
        updatedAt: apiReview.updated_at,
        hiddenReason: apiReview.hideReason || undefined,
        comments: comments.length > 0 ? comments : undefined,
        replyText: apiReview.replyText || undefined,
        replyDate: apiReview.replyDate || undefined,
        repliedBy: apiReview.repliedBy?.toString() || undefined,
        flagged: apiReview.flagged,
    };
}

/**
 * Maps array of API reviews to frontend reviews
 */
export function mapApiReviewsToFrontend(apiReviews: ApiReview[]): TReview[] {
    return apiReviews.map(mapApiReviewToFrontend);
}
