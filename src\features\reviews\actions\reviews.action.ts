'use server';

import API_ENDPOINTS from "@/shared/constants/api-routes";
import { executeApiRequest } from "@/shared/lib/helpers/actions.helpers";
import { REQUEST_TYPE } from "@/shared/types/action-helper.type";
import { ApiResponse } from "@/shared/types/utility.type";
import { TReview, TReviewLegacy, ReviewStats, ApiReviewsResponse, ApiReviewStats } from '../types/review.type';
import { mapApiReviewsToFrontend } from '../lib/review-mapper';
import { fetchReviewsAPI } from '../lib/custom-fetch';

interface GetReviewsParams {
  filter?: string;
  search?: string;
  page?: number;
  limit?: number;
}

/**
 * Get reviews for management page.
 * This action fetches the agent's reviews from the API.
 */
export async function getReviewsForManagement(params?: GetReviewsParams): Promise<ApiResponse<{
  reviews: TReview[];
  pagination: {
    total: number;
    totalPages: number;
    currentPage: number;
    perPage: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}>> {
  try {
    // Build query parameters
    const queryParams = new URLSearchParams();
    
    if (params?.filter && params.filter !== 'all') {
      queryParams.append('filter', params.filter);
    }
    
    if (params?.search && params.search.trim()) {
      queryParams.append('search', params.search.trim());
    }
    
    if (params?.page && params.page > 1) {
      queryParams.append('page', params.page.toString());
    }
    
    if (params?.limit && params.limit !== 10) {
      queryParams.append('limit', params.limit.toString());
    }
    
    const queryString = queryParams.toString();
    const url = queryString ? `${API_ENDPOINTS.GET_MY_REVIEWS}?${queryString}` : API_ENDPOINTS.GET_MY_REVIEWS;
    
    console.log('Fetching reviews from API endpoint:', url);
    
    // Use custom fetch function that handles the specific auth requirements
    const response = await fetchReviewsAPI<ApiReviewsResponse>({
      url,
      method: 'GET',
    });
    
    console.log('API Response:', response);
    
    if (!response.success) {
      console.error('API returned error:', response.message);
      return {
        success: false,
        message: response.message || 'Failed to fetch reviews',
        status: response.status || 500,
      };
    }

    if (!response.data || !response.data.reviews) {
      console.error('Invalid API response structure:', response.data);
      return {
        success: false,
        message: 'Invalid response structure from API',
        status: 500,
      };
    }

    // Map API response to frontend format
    const mappedReviews = mapApiReviewsToFrontend(response.data.reviews);
    console.log('Mapped reviews:', mappedReviews);
    
    return {
      success: true,
      data: {
        reviews: mappedReviews,
        pagination: response.data.pagination
      },
      message: 'Reviews fetched successfully',
      status: 200,
    };
  } catch (error) {
    console.error('Error fetching reviews:', error);
    return {
      success: false,
      message: 'An unexpected error occurred while fetching reviews',
      status: 500,
    };
  }
}

export const getReviews = async (): Promise<{ data: TReviewLegacy[] }> => {
  const reviews: TReviewLegacy[] = [];
  return { data: reviews };
};

/**
 * Get reviews stats from the API.
 * This action fetches the review statistics for the agent.
 */
export async function getReviewsStats(): Promise<ApiResponse<ReviewStats>> {
  try {
    console.log('Fetching reviews stats from API endpoint:', API_ENDPOINTS.GET_REVIEWS_STATS);
    
    const response = await executeApiRequest<ApiReviewStats>({
      method: REQUEST_TYPE.get,
      url: API_ENDPOINTS.GET_REVIEWS_STATS,
      withAuth: true,
    });
    
    console.log('Stats API Response:', response);
    
    if (!response.success) {
      console.error('Stats API returned error:', response.message);
      return {
        success: false,
        message: response.message || 'Failed to fetch review stats',
        status: response.status || 500,
      };
    }

    if (!response.data) {
      console.error('Invalid stats API response structure:', response.data);
      return {
        success: false,
        message: 'Invalid response structure from stats API',
        status: 500,
      };
    }

    // Map API response to frontend stats format
    const mappedStats: ReviewStats = {
      total: response.data.totalReviews,
      published: response.data.totalReviews, // Assuming all are published for now
      pending: 0, // Not provided by API
      flagged: response.data.flaggedCount,
      hidden: 0, // Not provided by API
      averageRating: response.data.averageRating,
      replied: response.data.repliedCount,
    };
    
    console.log('Mapped stats:', mappedStats);
    
    return {
      success: true,
      data: mappedStats,
      message: 'Review statistics fetched successfully',
      status: 200,
    };
  } catch (error) {
    console.error('Error fetching review stats:', error);
    return {
      success: false,
      message: 'An unexpected error occurred while fetching review stats',
      status: 500,
    };
  }
}

/**
 * Reply to a review
 */
export async function replyToReview(reviewId: string, replyText: string): Promise<ApiResponse<any>> {
  try {
    console.log('Replying to review:', reviewId, 'with text:', replyText);

    const formData = new FormData();
    formData.append('replyText', replyText);

    const response = await fetchReviewsAPI<any>({
      url: `${API_ENDPOINTS.REPLY_TO_REVIEW}/${reviewId}/reply`,
      method: 'POST',
      body: formData,
    });

    console.log('Reply API Response:', response);

    if (!response.success) {
      console.error('API returned error:', response.message);
      return {
        success: false,
        message: response.message || 'Failed to send reply',
        status: response.status || 500,
      };
    }

    return {
      success: true,
      data: response.data,
      message: 'Reply sent successfully',
      status: 200,
    };
  } catch (error) {
    console.error('Error sending reply:', error);
    return {
      success: false,
      message: 'An unexpected error occurred while sending reply',
      status: 500,
    };
  }
}

/**
 * Flag a review
 */
export async function flagReview(reviewId: string, reason: string): Promise<ApiResponse<any>> {
  try {
    console.log('Flagging review:', reviewId, 'with reason:', reason);

    const response = await fetchReviewsAPI<any>({
      url: `${API_ENDPOINTS.FLAG_REVIEW}/${reviewId}/flag`,
      method: 'PATCH',
      body: {
        flagged: true,
        reason: reason
      },
    });

    console.log('Flag review API Response:', response);

    if (!response.success) {
      console.error('API returned error:', response.message);
      return {
        success: false,
        message: response.message || 'Failed to flag review',
        status: response.status || 500,
      };
    }

    return {
      success: true,
      data: response.data,
      message: 'Review has been flagged successfully',
      status: 200,
    };
  } catch (error) {
    console.error('Error flagging review:', error);
    return {
      success: false,
      message: 'An unexpected error occurred while flagging review',
      status: 500,
    };
  }
}
